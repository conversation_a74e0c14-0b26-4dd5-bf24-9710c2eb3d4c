"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/kpi/KpiComponents.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KpiCard: function() { return /* binding */ KpiCard; },\n/* harmony export */   KpiGrid: function() { return /* binding */ KpiGrid; },\n/* harmony export */   \"default\": function() { return /* binding */ KpiComponentsDemo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! prop-types */ \"(app-pages-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Skeleton/Skeleton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/icons-material/InfoOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/InfoOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\r\n * KpiCard\r\n * Props:\r\n *  - title: string\r\n *  - value: string | number\r\n *  - delta: number (percent change, positive or negative)\r\n *  - caption: small text under value (string)\r\n *  - icon: React node (optional)\r\n *  - sparklineData: [{ value: number, label?: string }] (optional)\r\n *  - loading: bool\r\n *  - onClick: function (optional)\r\n *  - sx: additional sx styles\r\n */ function KpiCard(param) {\n    let { title, value, delta, caption, icon, sparklineData, loading, onClick, sx } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const positive = typeof delta === \"number\" ? delta >= 0 : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        elevation: 2,\n        sx: {\n            cursor: onClick ? \"pointer\" : \"default\",\n            height: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            ...sx\n        },\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 1,\n                flex: 1\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"flex-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"text.secondary\",\n                                    noWrap: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"text\",\n                                    width: 120,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h5\",\n                                    sx: {\n                                        mt: 0.5,\n                                        fontWeight: 600\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                caption && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    display: \"block\",\n                                    children: caption\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 1,\n                            children: [\n                                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"rounded\",\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        bgcolor: theme.palette.action.hover\n                                    },\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    title: \"M\\xe1s info\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    mt: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 0.5,\n                            children: typeof delta === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 0.5,\n                                children: [\n                                    positive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"success.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"error.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: positive ? \"success.main\" : \"error.main\",\n                                            fontWeight: 600\n                                        },\n                                        children: [\n                                            Math.abs(delta).toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        color: \"text.secondary\",\n                                        children: \"vs prev\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: \"\\xa0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            sx: {\n                                width: 120,\n                                height: 40\n                            },\n                            children: sparklineData && sparklineData.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__.LineChart, {\n                                    data: sparklineData,\n                                    margin: {\n                                        top: 0,\n                                        right: 0,\n                                        left: 0,\n                                        bottom: 0\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__.Line, {\n                                        type: \"monotone\",\n                                        dataKey: \"value\",\n                                        stroke: theme.palette.primary.main,\n                                        strokeWidth: 2,\n                                        dot: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(KpiCard, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function() {\n    return [\n        _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = KpiCard;\nKpiCard.propTypes = {\n    title: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string).isRequired,\n    value: prop_types__WEBPACK_IMPORTED_MODULE_17___default().oneOfType([\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number)\n    ]),\n    delta: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number),\n    caption: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n    icon: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().node),\n    sparklineData: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    loading: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().bool),\n    onClick: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().func),\n    sx: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * KpiGrid: simple responsive grid to layout KPI cards\r\n * Props:\r\n *  - items: array of { id, title, value, delta, caption, icon, sparklineData, loading }\r\n *  - columns: responsive columns object (optional)\r\n */ function KpiGrid(param) {\n    let { items = [], columns = {\n        xs: 12,\n        sm: 6,\n        md: 4\n    } } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Grid, {\n        container: true,\n        spacing: 2,\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Grid, {\n                item: true,\n                xs: columns.xs,\n                sm: columns.sm,\n                md: columns.md,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiCard, {\n                    ...item\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, item.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_c1 = KpiGrid;\nKpiGrid.propTypes = {\n    items: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    columns: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * Demo / Example usage default export\r\n * - Provides example KPIs and shows how to import/use KpiGrid\r\n */ function KpiComponentsDemo() {\n    const mockSpark = function() {\n        let start = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return Array.from({\n            length: 10\n        }).map((_, i)=>({\n                value: Math.round((start + Math.sin(i / 2) * 3 + i * 0.5) * 10) / 10\n            }));\n    };\n    const demoItems = [\n        {\n            id: \"ingresos\",\n            title: \"Ingresos (mes)\",\n            value: \"$ 1.250.000\",\n            delta: 8.2,\n            caption: \"vs mes anterior\",\n            sparklineData: mockSpark(100)\n        },\n        {\n            id: \"hectareas\",\n            title: \"Hect\\xe1reas atendidas\",\n            value: \"1.230 ha\",\n            delta: -2.4,\n            caption: \"\\xfaltimos 30 d\\xedas\",\n            sparklineData: mockSpark(50)\n        },\n        {\n            id: \"utilizacion\",\n            title: \"Utilizaci\\xf3n maquinaria\",\n            value: \"72 %\",\n            delta: 4.6,\n            caption: \"promedio flota\",\n            sparklineData: mockSpark(70)\n        },\n        {\n            id: \"costohora\",\n            title: \"Costo por hora\",\n            value: \"$ 3.800\",\n            delta: 1.1,\n            caption: \"comb, mano de obra\",\n            sparklineData: mockSpark(30)\n        },\n        {\n            id: \"ontime\",\n            title: \"Trabajos a tiempo\",\n            value: \"91 %\",\n            delta: 0.8,\n            caption: \"a tiempo\",\n            sparklineData: mockSpark(90)\n        },\n        {\n            id: \"mantenimiento\",\n            title: \"Mantenimientos pr\\xf3ximos\",\n            value: \"3 m\\xe1quinas\",\n            caption: \"en los pr\\xf3ximos 7 d\\xedas\",\n            sparklineData: mockSpark(20)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        p: 2,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"h6\",\n                sx: {\n                    mb: 2\n                },\n                children: \"KPIs reutilizables — demo\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiGrid, {\n                items: demoItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                mt: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Consejos:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: [\n                                        \"Pasa datos reales desde tu API y actualiza los props:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"value\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        \", \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"delta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 35\n                                        }, this),\n                                        \" y\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"sparklineData\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Para sparklines puedes usar datos de los \\xfaltimos 7/14/30 puntos (d\\xeda/semana) seg\\xfan tu granularidad.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Si usas TypeScript simplemente tipa las props y exporta los componentes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\n_c2 = KpiComponentsDemo;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"KpiCard\");\n$RefreshReg$(_c1, \"KpiGrid\");\n$RefreshReg$(_c2, \"KpiComponentsDemo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx\n"));

/***/ })

});