"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/kpi/KpiComponents.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KpiCard: function() { return /* binding */ KpiCard; },\n/* harmony export */   KpiGrid: function() { return /* binding */ KpiGrid; },\n/* harmony export */   \"default\": function() { return /* binding */ KpiComponentsDemo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! prop-types */ \"(app-pages-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Skeleton/Skeleton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/icons-material/InfoOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/InfoOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\r\n * KpiCard\r\n * Props:\r\n *  - title: string\r\n *  - value: string | number\r\n *  - delta: number (percent change, positive or negative)\r\n *  - caption: small text under value (string)\r\n *  - icon: React node (optional)\r\n *  - sparklineData: [{ value: number, label?: string }] (optional)\r\n *  - loading: bool\r\n *  - onClick: function (optional)\r\n *  - sx: additional sx styles\r\n */ function KpiCard(param) {\n    let { title, value, delta, caption, icon, sparklineData, loading, onClick, sx } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const positive = typeof delta === \"number\" ? delta >= 0 : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        elevation: 2,\n        sx: {\n            cursor: onClick ? \"pointer\" : \"default\",\n            height: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            ...sx\n        },\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 1,\n                flex: 1\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"flex-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"text.secondary\",\n                                    noWrap: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"text\",\n                                    width: 120,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h5\",\n                                    sx: {\n                                        mt: 0.5,\n                                        fontWeight: 600\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                caption && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    display: \"block\",\n                                    children: caption\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 1,\n                            children: [\n                                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"rounded\",\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        bgcolor: theme.palette.action.hover\n                                    },\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    title: \"M\\xe1s info\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    mt: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 0.5,\n                            children: typeof delta === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 0.5,\n                                children: [\n                                    positive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"success.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"error.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: positive ? \"success.main\" : \"error.main\",\n                                            fontWeight: 600\n                                        },\n                                        children: [\n                                            Math.abs(delta).toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        color: \"text.secondary\",\n                                        children: \"vs prev\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: \"\\xa0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            sx: {\n                                width: 120,\n                                height: 40\n                            },\n                            children: sparklineData && sparklineData.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__.LineChart, {\n                                    data: sparklineData,\n                                    margin: {\n                                        top: 0,\n                                        right: 0,\n                                        left: 0,\n                                        bottom: 0\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__.Line, {\n                                        type: \"monotone\",\n                                        dataKey: \"value\",\n                                        stroke: theme.palette.primary.main,\n                                        strokeWidth: 2,\n                                        dot: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(KpiCard, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function() {\n    return [\n        _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = KpiCard;\nKpiCard.propTypes = {\n    title: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string).isRequired,\n    value: prop_types__WEBPACK_IMPORTED_MODULE_17___default().oneOfType([\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number)\n    ]),\n    delta: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number),\n    caption: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n    icon: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().node),\n    sparklineData: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    loading: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().bool),\n    onClick: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().func),\n    sx: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * KpiGrid: simple responsive grid to layout KPI cards\r\n * Props:\r\n *  - items: array of { id, title, value, delta, caption, icon, sparklineData, loading }\r\n *  - columns: responsive columns object (optional)\r\n */ function KpiGrid(param) {\n    let { items = [], columns = {\n        xs: 12,\n        sm: 6,\n        md: 4\n    } } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n        container: true,\n        spacing: 2,\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                item: true,\n                xs: columns.xs,\n                sm: columns.sm,\n                md: columns.md,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiCard, {\n                    ...item\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, item.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_c1 = KpiGrid;\nKpiGrid.propTypes = {\n    items: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    columns: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * Demo / Example usage default export\r\n * - Provides example KPIs and shows how to import/use KpiGrid\r\n */ function KpiComponentsDemo() {\n    const mockSpark = function() {\n        let start = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return Array.from({\n            length: 10\n        }).map((_, i)=>({\n                value: Math.round((start + Math.sin(i / 2) * 3 + i * 0.5) * 10) / 10\n            }));\n    };\n    const demoItems = [\n        {\n            id: \"ingresos\",\n            title: \"Ingresos (mes)\",\n            value: \"$ 1.250.000\",\n            delta: 8.2,\n            caption: \"vs mes anterior\",\n            sparklineData: mockSpark(100)\n        },\n        {\n            id: \"hectareas\",\n            title: \"Hect\\xe1reas atendidas\",\n            value: \"1.230 ha\",\n            delta: -2.4,\n            caption: \"\\xfaltimos 30 d\\xedas\",\n            sparklineData: mockSpark(50)\n        },\n        {\n            id: \"utilizacion\",\n            title: \"Utilizaci\\xf3n maquinaria\",\n            value: \"72 %\",\n            delta: 4.6,\n            caption: \"promedio flota\",\n            sparklineData: mockSpark(70)\n        },\n        {\n            id: \"costohora\",\n            title: \"Costo por hora\",\n            value: \"$ 3.800\",\n            delta: 1.1,\n            caption: \"comb, mano de obra\",\n            sparklineData: mockSpark(30)\n        },\n        {\n            id: \"ontime\",\n            title: \"Trabajos a tiempo\",\n            value: \"91 %\",\n            delta: 0.8,\n            caption: \"a tiempo\",\n            sparklineData: mockSpark(90)\n        },\n        {\n            id: \"mantenimiento\",\n            title: \"Mantenimientos pr\\xf3ximos\",\n            value: \"3 m\\xe1quinas\",\n            caption: \"en los pr\\xf3ximos 7 d\\xedas\",\n            sparklineData: mockSpark(20)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        p: 2,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"h6\",\n                sx: {\n                    mb: 2\n                },\n                children: \"KPIs reutilizables — demo\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiGrid, {\n                items: demoItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                mt: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Consejos:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: [\n                                        \"Pasa datos reales desde tu API y actualiza los props:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"value\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        \", \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"delta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 35\n                                        }, this),\n                                        \" y\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"sparklineData\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Para sparklines puedes usar datos de los \\xfaltimos 7/14/30 puntos (d\\xeda/semana) seg\\xfan tu granularidad.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Si usas TypeScript simplemente tipa las props y exporta los componentes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\n_c2 = KpiComponentsDemo;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"KpiCard\");\n$RefreshReg$(_c1, \"KpiGrid\");\n$RefreshReg$(_c2, \"KpiComponentsDemo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx\n"));

/***/ })

});