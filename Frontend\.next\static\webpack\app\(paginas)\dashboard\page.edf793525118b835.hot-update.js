"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/kpi/KpiComponents.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KpiCard: function() { return /* binding */ KpiCard; },\n/* harmony export */   KpiGrid: function() { return /* binding */ KpiGrid; },\n/* harmony export */   \"default\": function() { return /* binding */ KpiComponentsDemo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! prop-types */ \"(app-pages-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Skeleton/Skeleton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/icons-material/InfoOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/InfoOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\r\n * KpiCard\r\n * Props:\r\n *  - title: string\r\n *  - value: string | number\r\n *  - delta: number (percent change, positive or negative)\r\n *  - caption: small text under value (string)\r\n *  - icon: React node (optional)\r\n *  - sparklineData: [{ value: number, label?: string }] (optional)\r\n *  - loading: bool\r\n *  - onClick: function (optional)\r\n *  - sx: additional sx styles\r\n */ function KpiCard(param) {\n    let { title, value, delta, caption, icon, sparklineData, loading, onClick, sx } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const positive = typeof delta === \"number\" ? delta >= 0 : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        elevation: 2,\n        sx: {\n            cursor: onClick ? \"pointer\" : \"default\",\n            height: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            ...sx\n        },\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 1,\n                flex: 1\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"flex-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"text.secondary\",\n                                    noWrap: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"text\",\n                                    width: 120,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h5\",\n                                    sx: {\n                                        mt: 0.5,\n                                        fontWeight: 600\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                caption && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    display: \"block\",\n                                    children: caption\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 1,\n                            children: [\n                                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"rounded\",\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        bgcolor: theme.palette.action.hover\n                                    },\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    title: \"M\\xe1s info\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    mt: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 0.5,\n                            children: typeof delta === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 0.5,\n                                children: [\n                                    positive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"success.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"error.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: positive ? \"success.main\" : \"error.main\",\n                                            fontWeight: 600\n                                        },\n                                        children: [\n                                            Math.abs(delta).toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        color: \"text.secondary\",\n                                        children: \"vs prev\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: \"\\xa0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            sx: {\n                                width: 120,\n                                height: 40\n                            },\n                            children: sparklineData && sparklineData.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__.LineChart, {\n                                    data: sparklineData,\n                                    margin: {\n                                        top: 0,\n                                        right: 0,\n                                        left: 0,\n                                        bottom: 0\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__.Line, {\n                                        type: \"monotone\",\n                                        dataKey: \"value\",\n                                        stroke: theme.palette.primary.main,\n                                        strokeWidth: 2,\n                                        dot: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(KpiCard, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function() {\n    return [\n        _barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = KpiCard;\nKpiCard.propTypes = {\n    title: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string).isRequired,\n    value: prop_types__WEBPACK_IMPORTED_MODULE_17___default().oneOfType([\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number)\n    ]),\n    delta: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number),\n    caption: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n    icon: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().node),\n    sparklineData: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    loading: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().bool),\n    onClick: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().func),\n    sx: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * KpiGrid: simple responsive grid to layout KPI cards\r\n * Props:\r\n *  - items: array of { id, title, value, delta, caption, icon, sparklineData, loading }\r\n *  - columns: responsive columns object (optional)\r\n */ function KpiGrid(param) {\n    let { items = [], columns = {\n        xs: 12,\n        sm: 6,\n        md: 4\n    } } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridLegacy, {\n        container: true,\n        spacing: 2,\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridLegacy, {\n                item: true,\n                xs: columns.xs,\n                sm: columns.sm,\n                md: columns.md,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiCard, {\n                    ...item\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, item.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_c1 = KpiGrid;\nKpiGrid.propTypes = {\n    items: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    columns: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * Demo / Example usage default export\r\n * - Provides example KPIs and shows how to import/use KpiGrid\r\n */ function KpiComponentsDemo() {\n    const mockSpark = function() {\n        let start = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return Array.from({\n            length: 10\n        }).map((_, i)=>({\n                value: Math.round((start + Math.sin(i / 2) * 3 + i * 0.5) * 10) / 10\n            }));\n    };\n    const demoItems = [\n        {\n            id: \"ingresos\",\n            title: \"Ingresos (mes)\",\n            value: \"$ 1.250.000\",\n            delta: 8.2,\n            caption: \"vs mes anterior\",\n            sparklineData: mockSpark(100)\n        },\n        {\n            id: \"hectareas\",\n            title: \"Hect\\xe1reas atendidas\",\n            value: \"1.230 ha\",\n            delta: -2.4,\n            caption: \"\\xfaltimos 30 d\\xedas\",\n            sparklineData: mockSpark(50)\n        },\n        {\n            id: \"utilizacion\",\n            title: \"Utilizaci\\xf3n maquinaria\",\n            value: \"72 %\",\n            delta: 4.6,\n            caption: \"promedio flota\",\n            sparklineData: mockSpark(70)\n        },\n        {\n            id: \"costohora\",\n            title: \"Costo por hora\",\n            value: \"$ 3.800\",\n            delta: 1.1,\n            caption: \"comb, mano de obra\",\n            sparklineData: mockSpark(30)\n        },\n        {\n            id: \"ontime\",\n            title: \"Trabajos a tiempo\",\n            value: \"91 %\",\n            delta: 0.8,\n            caption: \"a tiempo\",\n            sparklineData: mockSpark(90)\n        },\n        {\n            id: \"mantenimiento\",\n            title: \"Mantenimientos pr\\xf3ximos\",\n            value: \"3 m\\xe1quinas\",\n            caption: \"en los pr\\xf3ximos 7 d\\xedas\",\n            sparklineData: mockSpark(20)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        p: 2,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"h6\",\n                sx: {\n                    mb: 2\n                },\n                children: \"KPIs reutilizables — demo\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiGrid, {\n                items: demoItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                mt: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Consejos:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: [\n                                        \"Pasa datos reales desde tu API y actualiza los props:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"value\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        \", \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"delta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 35\n                                        }, this),\n                                        \" y\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"sparklineData\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Para sparklines puedes usar datos de los \\xfaltimos 7/14/30 puntos (d\\xeda/semana) seg\\xfan tu granularidad.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Si usas TypeScript simplemente tipa las props y exporta los componentes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\n_c2 = KpiComponentsDemo;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"KpiCard\");\n$RefreshReg$(_c1, \"KpiGrid\");\n$RefreshReg$(_c2, \"KpiComponentsDemo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx\n"));

/***/ })

});