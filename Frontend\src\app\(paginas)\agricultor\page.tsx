"use client";
import React, { useState, useEffect, useRef } from "react";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import { DataGrid as MuiDataGrid } from "@mui/x-data-grid";
import {
  Button,
  InputAdornment,
  Paper,
  SelectChangeEvent,
} from "@mui/material";
import { Dialog } from "@mui/material";
import { DialogContent } from "@mui/material";
import { DialogTitle } from "@mui/material";
import { DialogContentText } from "@mui/material";
import { FormControl } from "@mui/material";
import { FormHelperText } from "@mui/material";
import Grid from "@mui/material";
import { IconButton } from "@mui/material";
import { InputLabel } from "@mui/material";
import { MenuItem } from "@mui/material";
import { Select } from "@mui/material";
import { TextField } from "@mui/material";
import { Typography } from "@mui/material";
import Datatable from "../../components/table/DataTable";
import CloseIcon from "@mui/icons-material/Close";
import { Box } from "@mui/system";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import { Search as SearchIcon } from "@mui/icons-material";
import { Inter } from "next/font/google";
import AddCircleIcon from "@mui/icons-material/AddCircle";

const inter = Inter({ subsets: ["latin"] });

interface AgricultorGanaderoProps {
  onSearchChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onAddClick?: () => void;
}

interface Client {
  id: number;
  razonSocial: string;
  tipoCliente?: string;
  nombreContacto?: string;
  cargoContacto?: string;
  direccion: string;
  telefono: string;
  mail: string;
  lugar: string;
  provincia?: string;
  condFrenteIva: string;
  documento: string;
}

const AgricultorGanadero: React.FC<AgricultorGanaderoProps> = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedRow, setSelectedRow] = useState<Client | null>(null);
  const [open, setOpen] = useState(false);
  const [rows, setRows] = useState<Client[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isSearchBarOpen, setIsSearchBarOpen] = useState(false);
  const [filteredRows, setFilteredRows] = useState<Client[]>([]);
  const DataGrid = MuiDataGrid;
  const [personId, setPersonId] = useState<string>("");
  const [estadoModal, setEstadoModal] = useState<"add" | "update">("add");
  const [selectedClientId, setSelectedClientId] = useState<number | null>(null);
  const selectProvinciaRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const selectCondIvaRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const documentoRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const [error, setError] = useState<{ [key: string]: string }>({});

  const handleClientSelect = (clientId: number): void => {
    setSelectedClientId(clientId);
    const selectedClient = (rows as Client[]).find(
      (row) => row.id === clientId
    );
    if (selectedClient) {
      // Split the lugar field into localidad and provincia
      const [localidad, provincia] = selectedClient.lugar.split(" - ");

      setFormData({
        personRazonSocial: selectedClient.razonSocial,
        personTipoCliente: selectedClient.tipoCliente || "",
        personNombreContacto: selectedClient.nombreContacto || "",
        personCargoContacto: selectedClient.cargoContacto || "",
        personDomicilio: selectedClient.direccion,
        personTelefono: selectedClient.telefono,
        personMail: selectedClient.mail,
        personLocalidad: localidad,
        personProvincia: provincia,
        personCondFrenteIva: selectedClient.condFrenteIva,
        personDocumento: selectedClient.documento,
      });
    }
  };

  const [formData, setFormData] = useState({
    //personId: "",
    personRazonSocial: "",
    personTipoCliente: "",
    personNombreContacto: "",
    personCargoContacto: "",
    personDomicilio: "",
    personTelefono: "",
    personMail: "",
    personLocalidad: "",
    personProvincia: "",
    personCondFrenteIva: "",
    personDocumento: "",
  });

  const provincias = [
    "Buenos Aires",
    "Catamarca",
    "Chaco",
    "Chubut",
    "Córdoba",
    "Corrientes",
    "Entre Ríos",
    "Formosa",
    "Jujuy",
    "La Pampa",
    "La Rioja",
    "Mendoza",
    "Misiones",
    "Neuquén",
    "Río Negro",
    "Salta",
    "San Juan",
    "San Luis",
    "Santa Cruz",
    "Santa Fe",
    "Santiago del Estero",
    "Tierra del Fuego",
    "Tucumán",
  ];

  const tipoClienteOptions = [
    // Productores
    { value: "Productor(comercial)", category: "Productores", icon: "👨‍💼" },
    { value: "Productor(familiar)", category: "Productores", icon: "👨‍👩‍👧‍👦" },
    { value: "Estancia", category: "Productores", icon: "🏞️" },

    // Empresas y Organizaciones
    {
      value: "Empresa (persona jurídica, p. ej. SA / SRL)",
      category: "Empresas",
      icon: "🏢",
    },
    { value: "Cooperativa", category: "Empresas", icon: "🏘️" },
    {
      value: "Asociación/Consorcio/Entidad Gremial",
      category: "Empresas",
      icon: "🤝",
    },

    // Servicios y Contratistas
    {
      value: "Contratista(p. ej. otro que contrata equipo)",
      category: "Servicios",
      icon: "🚜",
    },
    {
      value: "Acopio/Industria/Exportador(silos, plantas, compradoras)",
      category: "Servicios",
      icon: "🏭",
    },

    // Sector Público y Otros
    {
      value: "Municipalidad/Estatal/Gubernamental",
      category: "Público",
      icon: "�",
    },
    {
      value: "Particular(pequeños clientes domésticos)",
      category: "Otros",
      icon: "👤",
    },
    { value: "Otro(para casos no previstos)", category: "Otros", icon: "❓" },
    { value: "No Especificado", category: "Otros", icon: "➖" },
  ];

  const condFrenteIvaOptions = [
    "IVA Responsable Inscripto",
    "IVA Responsable no Inscripto",
    "IVA no Responsable",
    "IVA Sujeto Exento",
    "Consumidor Final",
    "Responsable Monotributo",
    "Sujeto no Categorizado",
    "Proveedor del Exterior",
    "Cliente del Exterior",
    "IVA Liberado",
    "Pequeño Contribuyente Social",
    "Monotributista Social",
    "Pequeño Contribuyente Eventual",
  ];

  const handleOpenAdd = () => {
    setEstadoModal("add");
    clearFrom();
    setOpen(true);
  };

  const clearFrom = () => {
    setFormData({
      personRazonSocial: "",
      personTipoCliente: "",
      personNombreContacto: "",
      personCargoContacto: "",
      personDomicilio: "",
      personTelefono: "",
      personMail: "",
      personLocalidad: "",
      personProvincia: "",
      personCondFrenteIva: "",
      personDocumento: "",
    });
    setError({});
  };

  const handleClickClose = (
    _event: React.MouseEvent<HTMLElement>,
    reason?: string
  ) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    if (
      name === "personRazonSocial" ||
      name === "personLocalidad" ||
      name === "personNombreContacto" ||
      name === "personCargoContacto"
    ) {
      if (!/^[a-zA-ZÀ-ÿ\s]*$/.test(value)) {
        setError((prevError) => ({
          ...prevError,
          [name]: "Solo se permiten letras y espacios",
        }));
        return;
      } else {
        setError((prevError) => ({
          ...prevError,
          [name]: "",
        }));
      }
    }

    if (name === "personDomicilio") {
      if (!/^[a-zA-ZÀ-ÿ0-9\s.]*$/.test(value)) {
        setError((prevError) => ({
          ...prevError,
          personDomicilio:
            "Solo se permiten letras, números, espacios y puntos",
        }));
        return;
      } else {
        setError((prevError) => ({
          ...prevError,
          personDomicilio: "",
        }));
      }
    }

    if (name === "personTelefono") {
      // Eliminar todo lo que no sea número
      const cleaned = value.replace(/\D/g, "");

      // Limitar a 10 dígitos
      if (cleaned.length > 10) return;

      let formatted;
      if (cleaned.length <= 4) {
        formatted = cleaned;
      } else {
        formatted = `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`;
      }

      // Validar el formato completo
      const isValidFormat = /^\d{4}-\d{6}$/.test(formatted);

      setError((prevError) => ({
        ...prevError,
        personTelefono:
          formatted.length === 11 && !isValidFormat
            ? "Formato inválido. Debe ser 0000-000000"
            : "",
      }));

      setFormData((prevState) => ({
        ...prevState,
        [name]: formatted,
      }));
      return;
    }

    if (name === "personMail") {
      // Expresión regular para validar email
      const emailRegex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;

      // Si el campo no está vacío, validar el formato
      if (value && !emailRegex.test(value)) {
        setError((prevError) => ({
          ...prevError,
          personMail: "Formato de email inválido. Ejemplo: <EMAIL>",
        }));
      } else {
        setError((prevError) => ({
          ...prevError,
          personMail: "",
        }));
      }
    }

    if (name === "personDocumento") {
      // Eliminar todo lo que no sea número
      const cleaned = value.replace(/\D/g, "");

      // Limitar a 11 dígitos en total
      if (cleaned.length > 11) return;

      let formatted;
      if (cleaned.length <= 2) {
        formatted = cleaned;
      } else if (cleaned.length <= 10) {
        formatted = `${cleaned.slice(0, 2)}-${cleaned.slice(2)}`;
      } else {
        formatted = `${cleaned.slice(0, 2)}-${cleaned.slice(
          2,
          10
        )}-${cleaned.slice(10, 11)}`;
      }

      // Validar el formato completo
      const isValidFormat = /^\d{2}-\d{8}-\d{1}$/.test(formatted);

      setError((prevError) => ({
        ...prevError,
        personDocumento:
          formatted.length === 12 && !isValidFormat
            ? "Formato inválido. Debe ser 00-00000000-0"
            : "",
      }));

      setFormData((prevState) => ({
        ...prevState,
        [name]: formatted,
      }));
      return;
    }

    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  const handleSearchClick = () => {
    setIsSearchBarOpen(!isSearchBarOpen);
  };

  const columns = [
    {
      field: "empresa",
      headerName: "Empresa",
      width: 280,
      headerClassName: "custom-header",
      renderCell: (params: any) => (
        <Box sx={{ py: 1 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: "600",
              color: "#333",
              fontSize: "0.875rem",
              lineHeight: 1.2,
            }}
          >
            {params.row.razonSocial}
          </Typography>
          {params.row.tipoCliente && (
            <Typography
              variant="caption"
              sx={{
                color: "#666",
                fontSize: "0.75rem",
                fontStyle: "italic",
              }}
            >
              {params.row.tipoCliente}
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: "contacto",
      headerName: "Contacto",
      width: 220,
      headerClassName: "custom-header",
      renderCell: (params: any) => (
        <Box sx={{ py: 1 }}>
          {params.row.nombreContacto ? (
            <>
              <Typography
                variant="body2"
                sx={{
                  fontWeight: "600",
                  color: "#333",
                  fontSize: "0.875rem",
                  lineHeight: 1.2,
                }}
              >
                {params.row.nombreContacto}
              </Typography>
              {params.row.cargoContacto && (
                <Typography
                  variant="caption"
                  sx={{
                    color: "#666",
                    fontSize: "0.75rem",
                    fontStyle: "italic",
                  }}
                >
                  {params.row.cargoContacto}
                </Typography>
              )}
            </>
          ) : (
            <Typography
              variant="body2"
              sx={{
                color: "#999",
                fontStyle: "italic",
                fontSize: "0.875rem",
              }}
            >
              Sin contacto
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: "telefono",
      headerName: "Teléfono",
      width: 140,
      headerClassName: "custom-header",
    },
    {
      field: "mail",
      headerName: "Email",
      width: 180,
      headerClassName: "custom-header",
    },
    {
      field: "lugar",
      headerName: "Ubicación",
      width: 200,
      headerClassName: "custom-header",
    },
    {
      field: "documento",
      headerName: "Documento",
      width: 140,
      headerClassName: "custom-header",
    },
  ];

  /*AGREGAR AGRICULTOR/GANADERO*/
  const handleAddCliente = async () => {
    console.log("Iniciando envío...");
    setIsSubmitting(true);
    const lugar = `${formData.personLocalidad} - ${formData.personProvincia}`;
    const newPerson = {
      razonSocial: formData.personRazonSocial,
      tipoCliente: formData.personTipoCliente,
      nombreContacto: formData.personNombreContacto,
      cargoContacto: formData.personCargoContacto,
      direccion: formData.personDomicilio,
      telefono: formData.personTelefono,
      mail: formData.personMail,
      lugar: lugar,
      provincia: formData.personProvincia,
      condFrenteIva: formData.personCondFrenteIva,
      documento: formData.personDocumento,
    };

    // Llamar a la función de validación
    const errors = formData;
    console.log(errors);
    if (errors) {
      setError(errors);
      return;
    }

    // Mostrar cada dato individual en la consola
    console.log("Razón Social:", newPerson.razonSocial);
    console.log("Dirección:", newPerson.direccion);
    console.log("Teléfono:", newPerson.telefono);
    console.log("Mail:", newPerson.mail);
    console.log("Lugar:", lugar);
    console.log("Condición Frente IVA:", newPerson.condFrenteIva);
    console.log("Documento:", newPerson.documento);

    try {
      const res = await fetch("http://localhost:8080/api/agricultor", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newPerson),
      });

      // Verificar si la solicitud fue exitosa
      if (!res.ok) {
        throw new Error("Error al guardar el cliente");
      }

      clearFrom();
      const dataClientes = await fetchClientes();
      setRows(dataClientes);
      setOpen(false);
    } catch (error) {
      console.error("Error en la solicitud:", error);
    }
  };

  /*CARGAR AGRICULTOR/GANADERO EN EL DATAGRID*/
  const fetchClientes = async () => {
    try {
      const res = await fetch("http://localhost:8080/api/agricultor");
      if (!res.ok) {
        throw new Error("Error al obtener los clientes");
      }
      const dataClientes = await res.json();
      return dataClientes;
    } catch (error) {
      console.error("Error en la solicitud:", error);
      // Devolver un valor predeterminado en caso de error
      return [];
    }
  };

  useEffect(() => {
    const getData = async () => {
      const dataClientes = await fetchClientes();
      setRows(dataClientes);
    };

    getData();
  }, []);

  /*BUSCAR AGRICULTOR/GANADERO*/
  const handleSearhCliente = (event: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = event.target.value;
    setSearchTerm(searchValue);

    const filteredData = rows.filter((row: Client) => {
      return (
        row.razonSocial.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.direccion.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.telefono.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.mail.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.lugar.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.condFrenteIva.toLowerCase().includes(searchValue.toLowerCase()) ||
        row.documento.toLowerCase().includes(searchValue.toLowerCase())
      );
    });
    setFilteredRows(filteredData);
  };

  /*ELIMINAR AGRICULTOR/GANADERO*/
  const handleDeleteCliente = async (id: number) => {
    console.log("Cliente a eliminar:", id);

    try {
      const res = await fetch(`http://localhost:8080/api/agricultor/${id}`, {
        method: "DELETE",
      });

      if (res.ok) {
        console.log("Cliente eliminado exitosamente.");
        // Actualizar el estado de las filas después de eliminar un cliente
        const dataClientes = await fetchClientes();
        setRows(dataClientes);
      } else {
        console.error("Error al eliminar el cliente:", id);
      }
    } catch (error) {
      console.error("Error en la solicitud de eliminación:", error);
    }
  };

  /*CLICK BOTON MODIFICAR(LAPIZ)*/
  const handleEdit = async (id: any) => {
    try {
      const res = await fetch(`http://localhost:8080/api/agricultor/${id}`, {
        method: "GET",
      });

      if (res.ok) {
        console.log("Cliente obtenido exitosamente.");

        const agricultor = await res.json();
        setFormData({
          personRazonSocial: agricultor.razonSocial,
          personTipoCliente: agricultor.tipoCliente || "",
          personNombreContacto: agricultor.nombreContacto || "",
          personCargoContacto: agricultor.cargoContacto || "",
          personDomicilio: agricultor.direccion,
          personTelefono: agricultor.telefono,
          personMail: agricultor.mail,
          personLocalidad: agricultor.lugar.split(" - ")[0].trim(), // Corregido
          personProvincia: agricultor.lugar.split(" - ")[1].trim(), // Corregido
          personCondFrenteIva: agricultor.condFrenteIva,
          personDocumento: agricultor.documento,
        });
      } else {
        console.error("Error al modificar el cliente:", id);
      }
    } catch (error) {
      console.error("Error en la solicitud de eliminación:", error);
    }

    setEstadoModal("update");
    setOpen(true);
  };

  /*MODIFICAR AGRICULTOR/GANADERO PARA GAURDAR*/
  const handleUpdateCliente = async () => {
    if (!selectedRow) return;
    const lugar = `${formData.personLocalidad} - ${formData.personProvincia}`;
    const newPerson: Client = {
      id: selectedRow.id,
      razonSocial: formData.personRazonSocial,
      tipoCliente: formData.personTipoCliente,
      nombreContacto: formData.personNombreContacto,
      cargoContacto: formData.personCargoContacto,
      direccion: formData.personDomicilio,
      telefono: formData.personTelefono,
      mail: formData.personMail,
      lugar: lugar,
      provincia: formData.personProvincia,
      condFrenteIva: formData.personCondFrenteIva,
      documento: formData.personDocumento,
    };

    try {
      const res = await fetch(`http://localhost:8080/api/agricultor`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newPerson),
      });

      if (!res.ok) {
        throw new Error("Error al guardar el cliente");
      }

      // Update rows with proper typing
      const updatedRows = rows.map((row: Client) => {
        if (row.id === newPerson.id) {
          return newPerson;
        }
        return row;
      });

      setRows(updatedRows);
      clearFrom();
      setOpen(false);
    } catch (error) {
      console.error("Error en la solicitud:", error);
    }
  };

  const handleLocalidadKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === "Enter" && selectProvinciaRef.current) {
      selectProvinciaRef.current.focus();
    }
  };

  const handleProvinciaChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    handleInputChange(event);
    setTimeout(() => {
      if (selectCondIvaRef.current) {
        selectCondIvaRef.current.focus();
      }
    }, 0);
  };

  const handleCondIvaChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    handleInputChange(event);
    setTimeout(() => {
      if (documentoRef.current) {
        documentoRef.current.focus();
      }
    }, 0);
  };

  const handleSelectAgricultor = (id: number) => {
    const selectedAgricultor = rows.find((row) => row.id === id);

    if (selectedAgricultor) {
      const agricultor = {
        id: selectedAgricultor.id,
        razonSocial: selectedAgricultor.razonSocial,
      };

      // Guardar el agricultor seleccionado
      localStorage.setItem("selectedAgricultor", JSON.stringify(agricultor));

      // Redirigir de vuelta a la página de establecimiento
      window.location.href = "/establecimiento";
    }
  };

  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const labelStyles = {
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
    fontFamily: "Lexend, sans-serif", // Cambiado a Lexend
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mb: 3,
          mt: 3,
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="div"
            sx={{
              fontWeight: "bold",
              fontFamily: "Lexend, sans-serif", // Cambiado a Lexend
            }}
          >
            Agricultores
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: "text.secondary",
              mt: 1,
              fontFamily: `${inter.style.fontFamily}`, // Cambiado a Inter
            }}
          >
            Gestione la información de sus Agricultores
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={handleOpenAdd}
          sx={{
            bgcolor: "#2E7D32", // Color verde más oscuro
            color: "#ffffff",
            "&:hover": { bgcolor: "#0D9A0A" },
            height: "fit-content",
            alignSelf: "center",
            fontFamily: `${inter.style.fontFamily}`, // Agregado para usar Inter
          }}
          startIcon={<AddOutlinedIcon />}
        >
          Nuevo Agricultor
        </Button>
      </Box>

      <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Buscar..."
          value={searchTerm}
          onChange={handleSearhCliente}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <IconButton onClick={handleSearchClick}>
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />
        <Datatable
          columns={columns}
          rows={filteredRows.length > 0 ? filteredRows : rows}
          option={true}
          optionDeleteFunction={handleDeleteCliente}
          optionUpdateFunction={handleEdit}
          setSelectedRow={(row) => setSelectedRow(row as Client | null)}
          selectedRow={selectedRow}
          optionSelect={handleSelectAgricultor}
        />
      </Paper>

      <Dialog
        open={open}
        onClose={handleClickClose}
        maxWidth="lg"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            width: "1100px", // Ancho ampliado de 825px a 1100px
            maxWidth: "95vw", // Mejor aprovechamiento del viewport
            minHeight: "600px", // Altura mínima para mejor proporción
          },
        }}
      >
        <Box sx={{ p: 4 }}>
          {/* Padding ampliado para mejor espaciado */}
          <Box sx={{ mb: 2 }}>
            <DialogTitle
              sx={{
                p: 0,
                fontFamily: "Lexend, sans-serif",
                fontSize: "1.5rem",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Registrar nuevo agricultor/ganadero
            </DialogTitle>
            <DialogContentText
              sx={{
                p: 0,
                mt: 1,
                fontFamily: "Inter, sans-serif",
                color: "#666",
              }}
            >
              Complete la información del nuevo agricultor/ganadero a registrar.
            </DialogContentText>
          </Box>
          <IconButton
            aria-label="close"
            onClick={(event) => handleClickClose(event, "closeButtonClick")}
            sx={{ position: "absolute", right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
          <Box
            component="form"
            onSubmit={handleSubmit}
            className={inter.className}
          >
            <DialogContent sx={{ p: 0 }}>
              {/* Quitamos el padding del DialogContent */}
              <Grid container spacing={3}>
                {/* Espaciado ampliado entre elementos */}
                <Grid xs={12}>
                  <Grid container spacing={3}>
                    {/* Sección: Información de la Empresa/Entidad */}
                    <Grid xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Lexend, sans-serif",
                          fontWeight: "600",
                          color: "#333",
                          mb: 3,
                          mt: 1,
                          fontSize: "1.2rem",
                        }}
                      >
                        Información de la Empresa/Entidad
                      </Typography>
                    </Grid>

                    {/* Primera fila: Razón Social y Tipo de Cliente */}
                    <Grid2 xs={12} sm={6}>
                      <Typography variant="body2" sx={labelStyles}>
                        Razón Social
                      </Typography>
                      <TextField
                        placeholder="Ej: Estancia La Esperanza S.A."
                        variant="outlined"
                        id="razonSocial"
                        name="personRazonSocial"
                        type="text"
                        error={Boolean(error.personRazonSocial)}
                        helperText={error.personRazonSocial}
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        value={formData.personRazonSocial}
                        disabled={estadoModal === "update"}
                        fullWidth
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personRazonSocial &&
                                (error.personRazonSocial ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                          style: { fontFamily: "Inter, sans-serif" },
                        }}
                        sx={{
                          "& .MuiInputBase-input": {
                            fontFamily: "Inter, sans-serif",
                          },
                          "& .MuiFormHelperText-root": {
                            fontFamily: "Inter, sans-serif",
                          },
                        }}
                      />
                    </Grid2>
                    <Grid2 xs={12} sm={6}>
                      <Typography variant="body2" sx={labelStyles}>
                        Tipo de Cliente
                      </Typography>
                      <FormControl
                        fullWidth
                        error={Boolean(error.personTipoCliente)}
                      >
                        <Select
                          id="tipoCliente"
                          name="personTipoCliente"
                          value={formData.personTipoCliente}
                          onChange={(e) =>
                            handleInputChange(
                              e as React.ChangeEvent<HTMLInputElement>
                            )
                          }
                          displayEmpty
                          MenuProps={{
                            PaperProps: {
                              sx: {
                                maxHeight: 400,
                                width: "auto",
                                minWidth: "300px",
                                "& .MuiMenuItem-root": {
                                  fontFamily: "Lexend, sans-serif",
                                  fontSize: "0.875rem",
                                  fontWeight: 600,
                                  lineHeight: 1.5,
                                  padding: "12px 16px",
                                  whiteSpace: "normal",
                                  wordWrap: "break-word",
                                  minHeight: "auto",
                                  color: "#333",
                                  borderBottom: "1px solid #f0f0f0",
                                  "&:last-child": {
                                    borderBottom: "none",
                                  },
                                },
                              },
                            },
                          }}
                          sx={{
                            fontFamily: "Lexend, sans-serif",
                            "& .MuiSelect-select": {
                              fontFamily: "Lexend, sans-serif",
                              fontSize: "0.875rem",
                              fontWeight: 600,
                              lineHeight: 1.4,
                              padding: "12px 14px",
                              color: "#333",
                            },
                          }}
                        >
                          <MenuItem
                            value=""
                            disabled
                            sx={{
                              fontFamily: "Lexend, sans-serif",
                              fontStyle: "italic",
                              color: "#999",
                              fontSize: "0.875rem",
                              fontWeight: 600,
                              backgroundColor: "#fafafa",
                            }}
                          >
                            Seleccione un tipo de cliente
                          </MenuItem>
                          {tipoClienteOptions.map((tipo, index) => {
                            const isFirstInCategory =
                              index === 0 ||
                              tipoClienteOptions[index - 1].category !==
                                tipo.category;

                            return (
                              <MenuItem
                                key={`${tipo.value}-${index}`}
                                value={tipo.value}
                                sx={{
                                  fontFamily: "Lexend, sans-serif",
                                  fontSize: "0.875rem",
                                  fontWeight: 600,
                                  lineHeight: 1.5,
                                  padding: "12px 16px",
                                  whiteSpace: "normal",
                                  wordWrap: "break-word",
                                  minHeight: "auto",
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "8px",
                                  color: "#333",
                                  borderTop:
                                    isFirstInCategory && index > 0
                                      ? "1px solid #e0e0e0"
                                      : "none",
                                  marginTop:
                                    isFirstInCategory && index > 0
                                      ? "4px"
                                      : "0",
                                  paddingTop:
                                    isFirstInCategory && index > 0
                                      ? "16px"
                                      : "12px",
                                  "&:hover": {
                                    backgroundColor: "#f8f9fa",
                                    borderLeft: "3px solid #2196f3",
                                  },
                                  "&.Mui-selected": {
                                    backgroundColor: "#e3f2fd",
                                    borderLeft: "3px solid #1976d2",
                                    fontWeight: 600,
                                    "&:hover": {
                                      backgroundColor: "#bbdefb",
                                    },
                                  },
                                }}
                              >
                                <span
                                  style={{
                                    fontSize: "1rem",
                                    marginRight: "8px",
                                  }}
                                >
                                  {tipo.icon}
                                </span>
                                <Box
                                  sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    flex: 1,
                                  }}
                                >
                                  <span
                                    style={{
                                      fontFamily: "Lexend, sans-serif",
                                      fontWeight: 600,
                                      color: "#333",
                                    }}
                                  >
                                    {tipo.value}
                                  </span>
                                  <span
                                    style={{
                                      fontFamily: "Lexend, sans-serif",
                                      fontSize: "0.75rem",
                                      color: "#666",
                                      fontStyle: "italic",
                                      fontWeight: 500,
                                    }}
                                  >
                                    {tipo.category}
                                  </span>
                                </Box>
                              </MenuItem>
                            );
                          })}
                        </Select>
                        {error.personTipoCliente && (
                          <FormHelperText
                            sx={{ fontFamily: "Inter, sans-serif" }}
                          >
                            {error.personTipoCliente}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid2>

                    {/* Sección: Información del Contacto */}
                    <Grid2 xs={12}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Lexend, sans-serif",
                          fontWeight: "600",
                          color: "#333",
                          mb: 3,
                          mt: 3,
                          fontSize: "1.2rem",
                        }}
                      >
                        Información del Contacto
                      </Typography>
                    </Grid2>

                    {/* Segunda fila: Nombre Contacto y Cargo */}
                    <Grid2 xs={12} sm={6}>
                      <Typography variant="body2" sx={labelStyles}>
                        Nombre del Contacto
                      </Typography>
                      <TextField
                        placeholder="Ej: Juan Pérez"
                        variant="outlined"
                        id="nombreContacto"
                        name="personNombreContacto"
                        type="text"
                        error={Boolean(error.personNombreContacto)}
                        helperText={error.personNombreContacto}
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        value={formData.personNombreContacto}
                        fullWidth
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personNombreContacto &&
                                (error.personNombreContacto ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                          style: { fontFamily: "Inter, sans-serif" },
                        }}
                        sx={{
                          "& .MuiInputBase-input": {
                            fontFamily: "Inter, sans-serif",
                          },
                          "& .MuiFormHelperText-root": {
                            fontFamily: "Inter, sans-serif",
                          },
                        }}
                      />
                    </Grid2>
                    <Grid2 xs={12} sm={6}>
                      <Typography variant="body2" sx={labelStyles}>
                        Cargo (Opcional)
                      </Typography>
                      <TextField
                        placeholder="Ej: Encargado, Propietario, Administrador"
                        variant="outlined"
                        id="cargoContacto"
                        name="personCargoContacto"
                        type="text"
                        error={Boolean(error.personCargoContacto)}
                        helperText={error.personCargoContacto}
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        value={formData.personCargoContacto}
                        fullWidth
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personCargoContacto &&
                                (error.personCargoContacto ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                          style: { fontFamily: "Inter, sans-serif" },
                        }}
                        sx={{
                          "& .MuiInputBase-input": {
                            fontFamily: "Inter, sans-serif",
                          },
                          "& .MuiFormHelperText-root": {
                            fontFamily: "Inter, sans-serif",
                          },
                        }}
                      />
                    </Grid2>

                    {/* Tercera fila: Domicilio */}
                    <Grid2 xs={12}>
                      <Typography variant="body2" sx={labelStyles}>
                        Domicilio
                      </Typography>
                      <TextField
                        placeholder="Ej: Cordoba 123"
                        variant="outlined"
                        id="domicilio"
                        name="personDomicilio"
                        type="text"
                        error={Boolean(error.personDomicilio)}
                        helperText={error.personDomicilio}
                        fullWidth
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        value={formData.personDomicilio}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personDomicilio &&
                                (error.personDomicilio ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                          style: { fontFamily: "Inter, sans-serif" },
                        }}
                        sx={{
                          "& .MuiInputBase-input": {
                            fontFamily: "Inter, sans-serif",
                          },
                          "& .MuiFormHelperText-root": {
                            fontFamily: "Inter, sans-serif",
                          },
                        }}
                      />
                    </Grid2>

                    {/* Cuarta fila: Teléfono y Email */}
                    <Grid2 xs={12} sm={6}>
                      {/* Cambiado de sm={7} a sm={6} */}
                      <Typography variant="body2" sx={labelStyles}>
                        Teléfono
                      </Typography>
                      <TextField
                        placeholder="Ej: 0000-000000"
                        variant="outlined"
                        id="telefono"
                        name="personTelefono"
                        type="text"
                        error={Boolean(error.personTelefono)}
                        helperText={error.personTelefono}
                        fullWidth
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        value={formData.personTelefono}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personTelefono &&
                                (error.personTelefono ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid2>
                    <Grid2 xs={12} sm={6}>
                      {/* Cambiado de sm={5} a sm={6} */}
                      <Typography variant="body2" sx={labelStyles}>
                        Email
                      </Typography>
                      <TextField
                        placeholder="Ej: <EMAIL>"
                        variant="outlined"
                        id="email"
                        name="personMail"
                        type="email"
                        required
                        fullWidth
                        error={Boolean(error.personMail)}
                        helperText={error.personMail}
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        value={formData.personMail}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personMail &&
                                (error.personMail ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid2>

                    {/* Tercera fila: Localidad y Provincia */}
                    <Grid2 xs={12} sm={6}>
                      {/* Cambiado de sm={7} a sm={6} */}
                      <Typography variant="body2" sx={labelStyles}>
                        Localidad
                      </Typography>
                      <TextField
                        placeholder="Ej: Mercedes"
                        variant="outlined"
                        id="localidad"
                        name="personLocalidad"
                        type="text"
                        error={Boolean(error.personLocalidad)}
                        helperText={error.personLocalidad}
                        fullWidth
                        required
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        value={formData.personLocalidad}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personLocalidad &&
                                (error.personLocalidad ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid2>
                    <Grid2 xs={12} sm={6}>
                      {/* Cambiado de sm={5} a sm={6} */}
                      <Typography variant="body2" sx={labelStyles}>
                        Provincia
                      </Typography>
                      <FormControl
                        fullWidth
                        error={Boolean(error.personProvincia)}
                      >
                        <Select
                          name="personProvincia"
                          labelId="demo-simple-select-label"
                          fullWidth
                          value={formData.personProvincia}
                          onChange={(event: SelectChangeEvent<string>) => {
                            const syntheticEvent = {
                              target: {
                                name: "personProvincia",
                                value: event.target.value,
                              },
                            } as React.ChangeEvent<HTMLSelectElement>;
                            handleProvinciaChange(syntheticEvent);
                          }}
                          required
                          inputRef={selectProvinciaRef}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected) {
                              return "Seleccione una provincia";
                            }
                            return selected;
                          }}
                          endAdornment={
                            formData.personProvincia &&
                            !error.personProvincia ? (
                              <InputAdornment position="end">
                                <CheckCircleIcon color="success" />
                              </InputAdornment>
                            ) : null
                          }
                          MenuProps={{
                            PaperProps: {
                              style: {
                                maxHeight: 200,
                              },
                            },
                          }}
                          sx={{ minWidth: "200px" }}
                        >
                          {provincias.map((provincia) => (
                            <MenuItem key={provincia} value={provincia}>
                              {provincia}
                            </MenuItem>
                          ))}
                        </Select>
                        {error.personProvincia && (
                          <FormHelperText>
                            {error.personProvincia}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid2>

                    {/* Cuarta fila: Cond. Frente al IVA y Documento */}
                    <Grid2 xs={12} sm={6}>
                      {/* Cambiado de sm={7} a sm={6} */}
                      <Typography variant="body2" sx={labelStyles}>
                        Cond. Frente al IVA
                      </Typography>
                      <FormControl
                        fullWidth
                        error={Boolean(error.personCondFrenteIva)}
                      >
                        <Select
                          id="condIva"
                          name="personCondFrenteIva"
                          value={formData.personCondFrenteIva}
                          onChange={(event: SelectChangeEvent<string>) => {
                            const syntheticEvent = {
                              target: {
                                name: "personCondFrenteIva",
                                value: event.target.value,
                              },
                            } as React.ChangeEvent<HTMLSelectElement>;
                            handleCondIvaChange(syntheticEvent);
                          }}
                          inputRef={selectCondIvaRef}
                          displayEmpty
                          renderValue={(selected) => {
                            if (!selected) {
                              return "Seleccione una opción";
                            }
                            return selected;
                          }}
                          endAdornment={
                            formData.personCondFrenteIva &&
                            !error.personCondFrenteIva ? (
                              <InputAdornment position="end">
                                <CheckCircleIcon color="success" />
                              </InputAdornment>
                            ) : null
                          }
                        >
                          {condFrenteIvaOptions.map((option) => (
                            <MenuItem key={option} value={option}>
                              {option}
                            </MenuItem>
                          ))}
                        </Select>
                        {error.personCondFrenteIva && (
                          <FormHelperText>
                            {error.personCondFrenteIva}
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid2>
                    <Grid2 xs={12} sm={6}>
                      {" "}
                      {/* Cambiado de sm={5} a sm={6} */}
                      <Typography variant="body2" sx={labelStyles}>
                        Documento
                      </Typography>
                      <TextField
                        id="documento"
                        placeholder="Ej: 00-00000000-0"
                        name="personDocumento"
                        value={formData.personDocumento}
                        onChange={(
                          e: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >
                        ) =>
                          handleInputChange(
                            e as React.ChangeEvent<HTMLInputElement>
                          )
                        }
                        error={Boolean(error.personDocumento)}
                        helperText={error.personDocumento}
                        required
                        fullWidth
                        inputRef={documentoRef}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              {formData.personDocumento &&
                                (error.personDocumento ? (
                                  <ErrorIcon color="error" />
                                ) : (
                                  <CheckCircleIcon color="success" />
                                ))}
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Grid2>
                  </Grid>
                </Grid>
                <Grid xs={12} style={{ margin: "8px 0", padding: "8px" }}>
                  <Grid container spacing={1}>
                    <Grid
                      xs={12}
                      style={{
                        display: "flex",
                        justifyContent: "flex-end",
                        gap: "8px",
                      }}
                    >
                      <Button
                        type="button"
                        variant="outlined"
                        onClick={(event) =>
                          handleClickClose(event, "closeButtonClick")
                        }
                      >
                        Cancelar
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={<AddCircleIcon />}
                        sx={{
                          bgcolor: "#2E7D32",
                          color: "#ffffff",
                          "&:hover": { bgcolor: "#1B5E20" },
                          textTransform: "none",
                          "& .MuiSvgIcon-root": {
                            color: "#ffffff",
                          },
                        }}
                      >
                        {estadoModal === "add" ? "Registrar" : "Guardar"}
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </DialogContent>
          </Box>
        </Box>
      </Dialog>

      {/* <Datatable
        columns={columns}
        rows={filteredRows.length > 0 ? filteredRows : rows}
        option={true}
        optionDeleteFunction={handleDeleteCliente}
        optionUpdateFunction={handleEdit}
        setSelectedRow={setSelectedRow}
        selectedRow={selectedRow}
        optionSelect={handleSelectAgricultor}
      /> */}
      {/* </div> */}
      {/* </div> */}
    </>
  );
};

export default AgricultorGanadero;
